import * as vscode from 'vscode';
import axios from 'axios';

export interface GitHubUser {
    id: number;
    login: string;
    name: string;
    avatar_url: string;
    email: string;
}

interface GitHubTokenResponse {
    access_token: string;
    token_type: string;
    scope: string;
}

export class AuthService {
    private static readonly CLIENT_ID = 'Ov23lipEhk9VsLqeSWe3';
    private static readonly CLIENT_SECRET = '83fcd87a422c13ee857777f67a49ec40e6263cf3';
    private static readonly REDIRECT_URI = 'vscode://amazingly.amazingly-ext/auth-callback';
    private static readonly SCOPES = 'user:email repo';
    
    private context: vscode.ExtensionContext;
    private _user: GitHubUser | null = null;
    private _onDidChangeUser = new vscode.EventEmitter<GitHubUser | null>();
    
    public readonly onDidChangeUser = this._onDidChangeUser.event;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.loadStoredToken();
    }

    public get user(): GitHubUser | null {
        return this._user;
    }

    public get isAuthenticated(): boolean {
        return this._user !== null;
    }

    /**
     * Initiate GitHub OAuth flow
     */
    public async signIn(): Promise<void> {
        try {
            // Generate state for security
            const state = Math.random().toString(36).substring(2, 15);
            await this.context.secrets.store('github-oauth-state', state);

            // Build OAuth URL
            const authUrl = new URL('https://github.com/login/oauth/authorize');
            authUrl.searchParams.set('client_id', AuthService.CLIENT_ID);
            authUrl.searchParams.set('redirect_uri', AuthService.REDIRECT_URI);
            authUrl.searchParams.set('scope', AuthService.SCOPES);
            authUrl.searchParams.set('state', state);
            authUrl.searchParams.set('allow_signup', 'true');

            // Open OAuth URL in browser
            await vscode.env.openExternal(vscode.Uri.parse(authUrl.toString()));

            // Show progress message
            vscode.window.showInformationMessage(
                'Please complete the GitHub authentication in your browser.',
                'Cancel'
            );

        } catch (error) {
            console.error('Sign in error:', error);
            vscode.window.showErrorMessage('Failed to initiate sign in process.');
        }
    }

    /**
     * Handle OAuth callback with authorization code
     */
    public async handleCallback(uri: vscode.Uri): Promise<void> {
        try {
            const query = new URLSearchParams(uri.query);
            const code = query.get('code');
            const state = query.get('state');
            const error = query.get('error');

            if (error) {
                throw new Error(`OAuth error: ${error}`);
            }

            if (!code) {
                throw new Error('No authorization code received');
            }

            // Verify state
            const storedState = await this.context.secrets.get('github-oauth-state');
            if (state !== storedState) {
                throw new Error('Invalid state parameter');
            }

            // Exchange code for access token
            const tokenResponse = await axios.post<GitHubTokenResponse>('https://github.com/login/oauth/access_token', {
                client_id: AuthService.CLIENT_ID,
                client_secret: AuthService.CLIENT_SECRET,
                code: code,
                redirect_uri: AuthService.REDIRECT_URI
            }, {
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            const accessToken = tokenResponse.data.access_token;
            if (!accessToken) {
                throw new Error('No access token received');
            }

            // Store token securely
            await this.context.secrets.store('github-access-token', accessToken);

            // Fetch user information
            await this.fetchUserInfo(accessToken);

            // Update context and notify
            await vscode.commands.executeCommand('setContext', 'amazingly.authenticated', true);
            this._onDidChangeUser.fire(this._user);

            vscode.window.showInformationMessage(
                `Welcome, ${this._user?.name || this._user?.login}! You're now signed in to Amazingly.`
            );

        } catch (error) {
            console.error('Callback handling error:', error);
            vscode.window.showErrorMessage(`Authentication failed: ${error}`);
        }
    }

    /**
     * Sign out the user
     */
    public async signOut(): Promise<void> {
        try {
            // Clear stored tokens
            await this.context.secrets.delete('github-access-token');
            await this.context.secrets.delete('github-oauth-state');

            // Clear user data
            this._user = null;

            // Update context and notify
            await vscode.commands.executeCommand('setContext', 'amazingly.authenticated', false);
            this._onDidChangeUser.fire(null);

            vscode.window.showInformationMessage('You have been signed out of Amazingly.');

        } catch (error) {
            console.error('Sign out error:', error);
            vscode.window.showErrorMessage('Failed to sign out properly.');
        }
    }

    /**
     * Load stored token on extension activation
     */
    private async loadStoredToken(): Promise<void> {
        try {
            const token = await this.context.secrets.get('github-access-token');
            if (token) {
                await this.fetchUserInfo(token);
                await vscode.commands.executeCommand('setContext', 'amazingly.authenticated', true);
                this._onDidChangeUser.fire(this._user);
            } else {
                await vscode.commands.executeCommand('setContext', 'amazingly.authenticated', false);
            }
        } catch (error) {
            console.error('Failed to load stored token:', error);
            await vscode.commands.executeCommand('setContext', 'amazingly.authenticated', false);
        }
    }

    /**
     * Fetch user information from GitHub
     */
    private async fetchUserInfo(token: string): Promise<void> {
        try {
            const response = await axios.get<GitHubUser>('https://api.github.com/user', {
                headers: {
                    'Authorization': `token ${token}`,
                    'Accept': 'application/vnd.github.v3+json'
                }
            });

            this._user = response.data;
        } catch (error) {
            console.error('Failed to fetch user info:', error);
            throw new Error('Failed to fetch user information');
        }
    }

    /**
     * Get stored access token
     */
    public async getAccessToken(): Promise<string | null> {
        return await this.context.secrets.get('github-access-token') || null;
    }
} 