import * as vscode from 'vscode';
import { CommitInfo } from './gitService';
import { ReviewResult } from './aiReviewService';

export interface NotificationOptions {
    title: string;
    message: string;
    actions?: string[];
    type?: 'info' | 'warning' | 'error';
}

export class NotificationService {
    private static readonly NOTIFICATION_TIMEOUT = 10000; // 10 seconds
    private _context: vscode.ExtensionContext;
    private _pendingNotifications: Map<string, vscode.Disposable> = new Map();
    private _activeProgressCancellation: vscode.CancellationTokenSource | null = null;

    constructor(context: vscode.ExtensionContext) {
        this._context = context;
    }

    /**
     * Show a popup notification for new commits
     */
    public async showCommitNotification(commitInfo: CommitInfo): Promise<string | undefined> {
        const title = '🔍 New Commit Detected';
        const message = `"${commitInfo.message}" by ${commitInfo.author}`;
        const actions = ['Review Changes', 'Dismiss'];

        return this.showNotification({
            title,
            message,
            actions,
            type: 'info'
        });
    }

    /**
     * Show review progress notification
     */
    public showReviewProgressNotification(currentFile: string, progress: number): void {
        const title = '🤖 AI Review in Progress';
        const message = `Analyzing ${currentFile}... (${progress}%)`;
        
        // Use status bar for progress instead of popup to avoid spam
        vscode.window.setStatusBarMessage(`$(sync~spin) ${title}: ${message}`, 3000);
    }

    /**
     * Show review completion notification
     */
    public async showReviewCompleteNotification(results: ReviewResult[]): Promise<string | undefined> {
        // Cancel any active progress notification first
        this.cancelActiveProgress();

        const totalIssues = results.reduce((sum, result) => sum + result.suggestions.length, 0);
        const avgScore = results.reduce((sum, result) => sum + result.overallScore, 0) / results.length;
        
        let title: string;
        let message: string;
        let type: 'info' | 'warning' | 'error' = 'info';

        if (totalIssues === 0) {
            title = '✅ Review Complete';
            message = `No issues found! Code quality score: ${Math.round(avgScore)}%`;
            type = 'info';
        } else {
            const highSeverityIssues = results.reduce((sum, result) => 
                sum + result.suggestions.filter(s => s.severity === 'high').length, 0);
            
            if (highSeverityIssues > 0) {
                title = '⚠️ Review Complete - Issues Found';
                message = `Found ${totalIssues} issue(s) including ${highSeverityIssues} high severity. Score: ${Math.round(avgScore)}%`;
                type = 'warning';
            } else {
                title = '📝 Review Complete - Minor Issues';
                message = `Found ${totalIssues} minor issue(s). Score: ${Math.round(avgScore)}%`;
                type = 'info';
            }
        }

        return this.showNotification({
            title,
            message,
            actions: ['View Results', 'Dismiss'],
            type
        });
    }

    /**
     * Show error notification
     */
    public async showErrorNotification(error: string): Promise<string | undefined> {
        return this.showNotification({
            title: '❌ Review Failed',
            message: error,
            actions: ['Retry', 'Dismiss'],
            type: 'error'
        });
    }

    /**
     * Show notification about git repository initialization
     */
    public async showGitInitNotification(): Promise<string | undefined> {
        return this.showNotification({
            title: '📁 Git Repository Required',
            message: 'Amazingly works best with Git repositories. Initialize one to enable all features.',
            actions: ['Initialize Git', 'Open Folder', 'Dismiss'],
            type: 'info'
        });
    }

    /**
     * Show authentication required notification
     */
    public async showAuthRequiredNotification(): Promise<string | undefined> {
        return this.showNotification({
            title: '🔐 Authentication Required',
            message: 'Sign in with GitHub to use AI code review features.',
            actions: ['Sign In', 'Dismiss'],
            type: 'info'
        });
    }

    /**
     * Show notification when review is ready
     */
    public async showReviewReadyNotification(changedFiles: string[]): Promise<string | undefined> {
        const fileCount = changedFiles.length;
        const title = '🔍 Changes Ready for Review';
        const message = fileCount === 1 
            ? `1 file has changes ready for AI review`
            : `${fileCount} files have changes ready for AI review`;

        return this.showNotification({
            title,
            message,
            actions: ['Review All Changes', 'Review Selected', 'Later'],
            type: 'info'
        });
    }

    /**
     * Generic notification method
     */
    private async showNotification(options: NotificationOptions): Promise<string | undefined> {
        const { title, message, actions = [], type = 'info' } = options;
        const fullMessage = `${title}\n${message}`;

        let result: string | undefined;

        switch (type) {
            case 'error':
                result = await vscode.window.showErrorMessage(fullMessage, ...actions);
                break;
            case 'warning':
                result = await vscode.window.showWarningMessage(fullMessage, ...actions);
                break;
            case 'info':
            default:
                result = await vscode.window.showInformationMessage(fullMessage, ...actions);
                break;
        }

        return result;
    }

    /**
     * Show a bottom-right popup notification (using status bar message)
     */
    public showBottomRightNotification(message: string, timeout: number = NotificationService.NOTIFICATION_TIMEOUT): void {
        const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        statusBarItem.text = `$(bell) ${message}`;
        statusBarItem.tooltip = 'Click to open Amazingly AI Review';
        statusBarItem.command = 'amazingly.showWelcome';
        statusBarItem.show();

        // Auto-hide after timeout
        setTimeout(() => {
            statusBarItem.dispose();
        }, timeout);
    }

    /**
     * Show persistent notification in status bar
     */
    public showStatusBarNotification(message: string, command?: string): vscode.StatusBarItem {
        const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
        statusBarItem.text = message;
        if (command) {
            statusBarItem.command = command;
        }
        statusBarItem.show();
        return statusBarItem;
    }

    /**
     * Show progress notification with cancellation support
     */
    public async showProgressNotification<T>(
        title: string,
        task: (progress: vscode.Progress<{ message?: string; increment?: number }>, token: vscode.CancellationToken) => Promise<T>
    ): Promise<T> {
        // Cancel any existing progress notification
        this.cancelActiveProgress();

        // Create new cancellation token
        this._activeProgressCancellation = new vscode.CancellationTokenSource();

        try {
            return await vscode.window.withProgress(
                {
                    location: vscode.ProgressLocation.Notification,
                    title,
                    cancellable: true
                },
                async (progress, token) => {
                    // Listen for cancellation from either source
                    const disposable1 = token.onCancellationRequested(() => {
                        this._activeProgressCancellation?.cancel();
                    });

                    const disposable2 = this._activeProgressCancellation!.token.onCancellationRequested(() => {
                        // Progress will be cancelled automatically
                    });

                    try {
                        return await task(progress, this._activeProgressCancellation!.token);
                    } finally {
                        disposable1.dispose();
                        disposable2.dispose();
                    }
                }
            );
        } finally {
            this._activeProgressCancellation = null;
        }
    }

    /**
     * Cancel any active progress notification
     */
    public cancelActiveProgress(): void {
        if (this._activeProgressCancellation) {
            this._activeProgressCancellation.cancel();
            this._activeProgressCancellation.dispose();
            this._activeProgressCancellation = null;
        }
    }

    /**
     * Clear all pending notifications
     */
    public clearAllNotifications(): void {
        this._pendingNotifications.forEach(disposable => disposable.dispose());
        this._pendingNotifications.clear();
    }

    /**
     * Show welcome notification for first-time users
     */
    public async showWelcomeNotification(): Promise<string | undefined> {
        const hasShownWelcome = this._context.globalState.get('amazingly.hasShownWelcome', false);
        
        if (!hasShownWelcome) {
            const result = await this.showNotification({
                title: '🤖 Welcome to Amazingly AI Code Review!',
                message: 'Get started by signing in with GitHub to enable AI-powered code reviews.',
                actions: ['Sign In', 'Learn More', 'Later'],
                type: 'info'
            });

            // Mark that we've shown the welcome message
            await this._context.globalState.update('amazingly.hasShownWelcome', true);
            return result;
        }

        return undefined;
    }

    /**
     * Show notification with custom timeout
     */
    public showTimedNotification(message: string, timeout: number = 5000): void {
        const notification = vscode.window.setStatusBarMessage(message, timeout);
        
        const notificationId = Date.now().toString();
        this._pendingNotifications.set(notificationId, notification);
        
        setTimeout(() => {
            this._pendingNotifications.delete(notificationId);
        }, timeout);
    }

    /**
     * Show notification for file-specific review completion
     */
    public async showFileReviewCompleteNotification(filePath: string, issueCount: number): Promise<string | undefined> {
        const fileName = filePath.split('/').pop() || filePath;
        const title = issueCount === 0 ? '✅ File Review Complete' : '📝 File Review Complete';
        const message = issueCount === 0 
            ? `${fileName}: No issues found!`
            : `${fileName}: Found ${issueCount} issue(s)`;

        return this.showNotification({
            title,
            message,
            actions: ['View Details', 'Dismiss'],
            type: issueCount === 0 ? 'info' : 'warning'
        });
    }

    public dispose(): void {
        this.clearAllNotifications();
    }
}
