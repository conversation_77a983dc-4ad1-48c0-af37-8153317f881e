Amazingly AI VS Code Extension — Complete Feature & User Flow Prompt
Overview
We are building a Visual Studio Code extension called Amazingly.
Amazingly delivers AI-powered code reviews directly inside the developer’s IDE, streamlining code quality and security by providing real-time, context-aware feedback, branch/PR reviews, and inline suggestions—before code is even pushed for review.

Product Goals
Help developers catch bugs, security issues, and code smells instantly while coding.

Allow full AI review of changes on any git branch before PRs are raised.

Provide inline suggestions and one-click fixes, just like a senior dev would.

Ensure a seamless, secure, and enjoyable UX, matching or exceeding competitors like CodeRabbit.

Core Features and User Flows
1. Onboarding & Authentication
User Flow:

User installs the extension from VS Code Marketplace.

On first launch (clicking the sidebar icon), user sees a welcome screen highlighting the main benefits:

“Analyze PRs with full codebase context.”

“Catch bugs and security issues instantly.”

User clicks Sign in with GitHub.

Extension opens an OAuth flow in the browser for secure login.

After success, the extension completes the login (via redirect or token paste).

User’s profile is displayed in the extension sidebar.

2. Git Repository Detection
User Flow:

On activation, the extension checks if the current workspace is a git repository.

If it isn’t, display:
“This folder doesn’t have a Git repository. Click to initialize Git or open an existing repository.”

On user click, run git init or prompt to open/add a repo.

Once a git repo is available, proceed to next steps.

3. Branch and File Change Review
User Flow:

Extension detects the active git branch (e.g., feature/my-change) and the target base branch (e.g., main).

Display a dropdown to select target branch if needed.

Show a list of all files with changes in the active branch compared to the base.

User clicks “Review all changes” (or selects individual files to review).

Extension starts the AI review process, indicating progress (e.g., “Analyzing changes…”).

As each file is reviewed:

Display AI-powered inline suggestions/comments for each relevant code line/block.

Mark suggestions as Potential Issue, Refactor Suggestion, Best Practice, Security Issue, or Test Coverage.

4. Inline AI Suggestions & Review Panel
User Flow:

Inline AI feedback appears directly in the code editor (via code lens, comment bubbles, or in a review panel).

Each suggestion shows:

Issue type (icon and text, e.g., Security, Refactor)

Description (what and why)

Highlighted code snippet

Suggested fix (with copy/apply option)

User can:

Accept/fix with one click (applies the suggestion to the code)

Ignore/dismiss the suggestion

Expand for more context or “Why?” explanation

5. Review Progress & History (Session-Based)
User Flow:

A status/progress panel shows the review state:

“Setting up”, “Analyzing changes”, “Review complete”

The panel lists all current and most recent review sessions/files.

After completion, user can see a summary of all suggestions for that session.

6. Secure & Private Code Handling
User Flow:

The extension never stores user code permanently.

All review requests and data are transmitted securely (encryption in transit).

Tokens are stored using VS Code SecretStorage API.

Optional/Advanced Features (If Time Allows)
Customizable Review Settings: Let users set review strictness, focus areas (e.g., only security), or exclude specific files/folders.

Integrations with GitHub/GitLab/Bitbucket: Option to post review comments directly to PRs.

Team/Workspace Collaboration: Share review settings/results with a team.

Technical Requirements
Use TypeScript for all extension logic.

Use VS Code Extension API for sidebar panels, tree views, webviews, authentication, and editor decorations.

Use the Git Extension API or system git commands to detect branches and file changes.

Use SecretStorage for secure token storage.

Use a webview or panel for rich UI where needed (for onboarding, review summaries, etc).

Support dark mode and use a modern, professional look.

Summary Table for Developers
Feature	How It Works in the Extension
Onboarding/Auth	Welcome screen > GitHub OAuth > Show user profile
Git Detection	Checks for git repo, prompts/init as needed
Branch/PR Review	Detects base and feature branch, lists changed files, “Review all changes” button
Inline Suggestions	Inline in editor + panel, per-file and per-line, with issue/fix/why/copy/apply options
Progress Panel	“Analyzing”, progress bar/spinner, session review log
Security	No code stored, encrypted transfers, SecretStorage for tokens
Session Review History	Last reviews and files shown in sidebar/panel (no persistent cross-session history unless you choose to add it)