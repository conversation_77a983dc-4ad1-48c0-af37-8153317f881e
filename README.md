# Amazingly AI Code Review

> AI-powered code reviews directly in your IDE with real-time feedback and branch analysis

Amazingly delivers intelligent code reviews inside Visual Studio Code, helping developers catch bugs, security issues, and code smells instantly while coding. Get full AI review of changes on any git branch before PRs are raised, with inline suggestions and one-click fixes.

## 🚀 Features

### 🔐 Secure GitHub Authentication
- One-click GitHub OAuth integration
- Secure token storage using VS Code SecretStorage
- User profile display with avatar and GitHub handle

### 🤖 AI-Powered Code Review
- Real-time code analysis using Deepseek V3
- Context-aware suggestions with full codebase understanding
- Security issue detection and vulnerability assessment
- Code quality and best practice recommendations

### 🌿 Branch & File Change Analysis
- Automatic detection of current git branch and changes
- Compare changes against base branch (e.g., main)
- Review individual files or entire changesets
- Progress tracking and review history

### 💡 Inline Suggestions & Fixes
- Inline code lens with AI suggestions
- One-click apply fixes and improvements
- Categorized feedback (Security, Refactor, Best Practice, etc.)
- Expandable explanations for "Why?" context

### 🎨 Modern UI/UX
- Beautiful, dark-mode friendly interface
- Activity bar integration with custom icon
- Responsive webview panels
- VS Code native styling and theming

## 📋 Requirements

- Visual Studio Code 1.100.0 or higher
- Git repository in your workspace
- Internet connection for AI analysis
- GitHub account for authentication

## 🚀 Getting Started

1. **Install the Extension**
   - Search for "Amazingly AI Code Review" in VS Code Marketplace
   - Click Install

2. **Sign in with GitHub**
   - Click the robot icon (🤖) in the activity bar
   - Click "Sign in with GitHub" 
   - Complete OAuth flow in browser

3. **Start Reviewing Code**
   - Open a workspace with a Git repository
   - Make changes to your code
   - Use "Review Current Branch" to analyze changes
   - Apply AI suggestions with one-click

## 🎯 User Flow

### First Launch
1. Welcome screen with benefits highlighting
2. GitHub OAuth authentication
3. User profile display in sidebar

### Daily Usage
1. Extension detects Git branch and file changes
2. Click "Review all changes" or select specific files
3. AI analyzes code and provides inline suggestions
4. Apply fixes, ignore, or get more context
5. Review progress tracked in dedicated panel

## 🔧 Extension Settings

This extension contributes the following settings:

* `amazingly.autoReview`: Automatically review changes on file save
* `amazingly.reviewLevel`: Set review strictness (basic, standard, strict)
* `amazingly.excludePatterns`: File patterns to exclude from review

## 🛡️ Security & Privacy

- **No Code Storage**: Your code is never stored permanently
- **Encrypted Transit**: All data transmitted securely via HTTPS
- **Token Security**: GitHub tokens stored using VS Code SecretStorage
- **Privacy First**: Only review data is sent to AI service, not your entire codebase

## 🐛 Known Issues

- Manual code entry fallback not yet implemented
- Large codebases may take longer to analyze
- Some advanced Git features not yet supported

## 📝 Release Notes

### 0.1.1

Enhanced AI Code Review with Git Integration

**New Features:**
- **Git Commit Detection**: Automatically detects new commits and shows review popup
- **AI-Powered Code Review**: Integration with Deepseek V3 for intelligent code analysis
- **Branch & File Change Review**: Complete implementation of branch comparison and file analysis
- **Real-time Notifications**: Bottom-right popups for new commits and review status
- **Progress Tracking**: Visual progress indicators during AI review process
- **Review Results Display**: Comprehensive review results with severity levels and scores

**Improvements:**
- Centralized Git operations through GitService
- Enhanced UI with real-time review progress
- Better error handling and user feedback
- Automatic file change detection and monitoring

### 0.0.1

Initial release of Amazingly AI Code Review

**Features:**
- GitHub OAuth authentication
- Welcome screen and user onboarding
- Activity bar integration
- Git repository detection
- Foundation for AI code review

---

## 🔗 Links

- [GitHub Repository](https://github.com/amazingly/amazingly-vscode-ext)
- [Issue Tracker](https://github.com/amazingly/amazingly-vscode-ext/issues)
- [VS Code Marketplace](https://marketplace.visualstudio.com/items?itemName=amazingly.amazingly-ext)

## 🤝 Contributing

We welcome contributions! Please see our GitHub repository for details.

## 📄 License

This extension is licensed under the [MIT License](LICENSE).

---

**Enjoy coding with AI-powered reviews!** 🎉
