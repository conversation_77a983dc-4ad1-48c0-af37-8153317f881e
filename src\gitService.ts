import * as vscode from 'vscode';
import * as cp from 'child_process';
import * as path from 'path';
import * as fs from 'fs';
import * as chokidar from 'chokidar';

export interface GitStatus {
    isRepo: boolean;
    repoPath?: string;
    activeBranch?: string;
    baseBranch?: string;
    branches?: string[];
    changedFiles?: string[];
    lastCommitHash?: string;
}

export interface FileChange {
    path: string;
    status: 'added' | 'modified' | 'deleted' | 'renamed';
    content?: string;
    diff?: string;
}

export interface CommitInfo {
    hash: string;
    message: string;
    author: string;
    date: string;
    changedFiles: FileChange[];
}

export class GitService {
    private _gitStatus: GitStatus = { isRepo: false };
    private _onDidChangeGitStatus = new vscode.EventEmitter<GitStatus>();
    private _onDidCommit = new vscode.EventEmitter<CommitInfo>();
    private _gitWatcher?: chokidar.FSWatcher;
    private _lastKnownCommitHash?: string;
    private _context: vscode.ExtensionContext;
    private _unreviewedCommits: string[] = [];
    private _updateTimeout?: NodeJS.Timeout;

    public readonly onDidChangeGitStatus = this._onDidChangeGitStatus.event;
    public readonly onDidCommit = this._onDidCommit.event;

    constructor(context?: vscode.ExtensionContext) {
        this._context = context || {} as vscode.ExtensionContext;
        this.initialize();
    }

    public get gitStatus(): GitStatus {
        return this._gitStatus;
    }

    public async refreshGitStatus(): Promise<void> {
        console.log('Manual git status refresh requested');
        await this.updateGitStatus();
        this.setupGitWatcher(); // Re-setup watcher in case repo was just created
    }

    private async initialize(): Promise<void> {
        await this.updateGitStatus();
        this.setupGitWatcher();
        
        // Listen for workspace folder changes
        vscode.workspace.onDidChangeWorkspaceFolders(() => {
            this.updateGitStatus();
            this.setupGitWatcher();
        });
    }

    private async updateGitStatus(): Promise<void> {
        console.log('Updating git status...');
        const workspaceFolders = vscode.workspace.workspaceFolders;
        const previousStatus = { ...this._gitStatus };

        this._gitStatus = { isRepo: false };

        if (workspaceFolders && workspaceFolders.length > 0) {
            for (const folder of workspaceFolders) {
                try {
                    const gitDir = vscode.Uri.joinPath(folder.uri, '.git');
                    await vscode.workspace.fs.stat(gitDir);

                    this._gitStatus = {
                        isRepo: true,
                        repoPath: folder.uri.fsPath
                    };

                    console.log(`Found git repo at: ${folder.uri.fsPath}`);
                    await this.updateBranchInfo();
                    await this.updateChangedFiles();
                    await this.updateLastCommitHash();
                    break;
                } catch {
                    // .git directory doesn't exist in this folder
                }
            }
        }

        // Emit change event if status changed
        const statusChanged = JSON.stringify(previousStatus) !== JSON.stringify(this._gitStatus);
        console.log(`Git status changed: ${statusChanged}`, this._gitStatus);
        if (statusChanged) {
            this._onDidChangeGitStatus.fire(this._gitStatus);
        }
    }

    private async updateBranchInfo(): Promise<void> {
        if (!this._gitStatus.isRepo || !this._gitStatus.repoPath) {
            return;
        }

        try {
            // Get all branches
            const branchOutput = await this.execGit(['branch', '--format', '%(refname:short)'], this._gitStatus.repoPath);
            this._gitStatus.branches = branchOutput.split('\n').map(b => b.trim()).filter(Boolean);

            // Get active branch
            const activeOutput = await this.execGit(['branch', '--show-current'], this._gitStatus.repoPath);
            this._gitStatus.activeBranch = activeOutput.trim();

            // Set default base branch
            if (!this._gitStatus.baseBranch || !this._gitStatus.branches.includes(this._gitStatus.baseBranch)) {
                this._gitStatus.baseBranch = this._gitStatus.branches.includes('main') 
                    ? 'main' 
                    : (this._gitStatus.branches.includes('master') ? 'master' : this._gitStatus.branches[0]);
            }
        } catch (error) {
            console.error('Failed to update branch info:', error);
        }
    }

    private async updateChangedFiles(): Promise<void> {
        if (!this._gitStatus.isRepo || !this._gitStatus.repoPath ||
            !this._gitStatus.activeBranch || !this._gitStatus.baseBranch) {
            this._gitStatus.changedFiles = [];
            return;
        }

        try {
            // Get committed changes between base and current branch
            let changedFiles: string[] = [];
            try {
                const diffOutput = await this.execGit([
                    'diff',
                    '--name-only',
                    `${this._gitStatus.baseBranch}...${this._gitStatus.activeBranch}`
                ], this._gitStatus.repoPath);
                changedFiles = diffOutput.split('\n').map(f => f.trim()).filter(Boolean);
            } catch (diffError) {
                console.warn('Could not get branch diff:', diffError);
            }

            // Also include working directory changes (staged and unstaged)
            try {
                const statusOutput = await this.execGit(['status', '--porcelain'], this._gitStatus.repoPath);
                const workingDirChanges = statusOutput
                    .split('\n')
                    .filter(line => line.trim())
                    .map(line => line.substring(3).trim()) // Remove status prefix
                    .filter(Boolean);

                // Combine and deduplicate
                changedFiles = [...new Set([...changedFiles, ...workingDirChanges])];
            } catch (statusError) {
                console.warn('Could not get working directory status:', statusError);
            }

            this._gitStatus.changedFiles = changedFiles;
        } catch (error) {
            console.error('Failed to update changed files:', error);
            this._gitStatus.changedFiles = [];
        }
    }

    private async updateLastCommitHash(): Promise<void> {
        if (!this._gitStatus.isRepo || !this._gitStatus.repoPath) {
            return;
        }

        try {
            const hashOutput = await this.execGit(['rev-parse', 'HEAD'], this._gitStatus.repoPath);
            const currentHash = hashOutput.trim();

            // Check if this is a new commit
            if (this._lastKnownCommitHash !== undefined && this._lastKnownCommitHash !== currentHash) {
                console.log(`🔥 NEW COMMIT DETECTED: ${currentHash} (previous: ${this._lastKnownCommitHash})`);
                console.log(`🔥 About to fire commit event...`);

                const commitInfo = await this.getCommitInfo(currentHash);
                console.log(`🔥 Commit info retrieved:`, commitInfo);

                await this.addUnreviewedCommit(currentHash);
                console.log(`🔥 Added to unreviewed commits, now firing event...`);

                this._onDidCommit.fire(commitInfo);
                console.log(`🔥 Commit event fired successfully!`);
            } else if (this._lastKnownCommitHash === undefined) {
                // First time initialization - just set the hash without firing event
                console.log(`Initial commit hash set: ${currentHash}`);
            } else {
                console.log(`No new commit detected. Current: ${currentHash}, Last known: ${this._lastKnownCommitHash}`);
            }

            this._gitStatus.lastCommitHash = currentHash;
            this._lastKnownCommitHash = currentHash;
        } catch (error) {
            console.error('Failed to update last commit hash:', error);
        }
    }

    private setupGitWatcher(): void {
        console.log('Setting up git watcher...');

        // Clean up existing watcher
        if (this._gitWatcher) {
            console.log('Closing existing git watcher');
            this._gitWatcher.close();
        }

        if (!this._gitStatus.isRepo || !this._gitStatus.repoPath) {
            console.log('No git repo found, skipping watcher setup');
            return;
        }

        console.log(`Setting up git watcher for: ${this._gitStatus.repoPath}`);

        // Watch for changes in .git directory (commits, branch changes, etc.)
        const gitPath = path.join(this._gitStatus.repoPath, '.git');

        this._gitWatcher = chokidar.watch([
            path.join(gitPath, 'HEAD'),
            path.join(gitPath, 'refs', 'heads', '*'),
            path.join(gitPath, 'logs', 'HEAD'),
            path.join(gitPath, 'index'), // Watch for staged changes
            path.join(gitPath, 'COMMIT_EDITMSG'), // Watch for commit messages
            // Watch for working directory changes in common file types
            path.join(this._gitStatus.repoPath, '**/*.{js,ts,jsx,tsx,py,java,cpp,c,cs,go,rs,php,rb,html,css,scss,json,md}')
        ], {
            ignoreInitial: true,
            persistent: true,
            ignored: [
                '**/node_modules/**',
                '**/.git/**',
                '**/dist/**',
                '**/build/**',
                '**/*.log',
                '**/.vscode/**'
            ]
        });

        this._gitWatcher.on('change', async (filePath) => {
            console.log(`Git watcher detected change: ${filePath}`);
            // Debounce rapid changes
            if (this._updateTimeout) {
                clearTimeout(this._updateTimeout);
            }
            this._updateTimeout = setTimeout(async () => {
                console.log('Updating git status due to file change...');
                await this.updateGitStatus();
            }, 500); // Wait 500ms before updating
        });

        this._gitWatcher.on('add', async (filePath) => {
            console.log(`Git watcher detected file added: ${filePath}`);
            if (this._updateTimeout) {
                clearTimeout(this._updateTimeout);
            }
            this._updateTimeout = setTimeout(async () => {
                console.log('Updating git status due to file addition...');
                await this.updateGitStatus();
            }, 500);
        });

        this._gitWatcher.on('unlink', async (filePath) => {
            console.log(`Git watcher detected file removed: ${filePath}`);
            if (this._updateTimeout) {
                clearTimeout(this._updateTimeout);
            }
            this._updateTimeout = setTimeout(async () => {
                console.log('Updating git status due to file removal...');
                await this.updateGitStatus();
            }, 500);
        });

        this._gitWatcher.on('ready', () => {
            console.log('Git watcher is ready and watching for changes');
        });

        this._gitWatcher.on('error', (error) => {
            console.error('Git watcher error:', error);
        });
    }

    public async getCommitInfo(commitHash?: string): Promise<CommitInfo> {
        if (!this._gitStatus.isRepo || !this._gitStatus.repoPath) {
            throw new Error('Not in a git repository');
        }

        const hash = commitHash || 'HEAD';
        
        try {
            // Get commit details
            const commitOutput = await this.execGit([
                'show', 
                '--format=%H|%s|%an|%ad', 
                '--date=iso',
                '--name-status',
                hash
            ], this._gitStatus.repoPath);

            const lines = commitOutput.split('\n');
            const [commitHash, message, author, date] = lines[0].split('|');
            
            // Parse changed files
            const changedFiles: FileChange[] = [];
            for (let i = 2; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;
                
                const [status, filePath] = line.split('\t');
                if (filePath) {
                    changedFiles.push({
                        path: filePath,
                        status: this.mapGitStatus(status)
                    });
                }
            }

            return {
                hash: commitHash,
                message,
                author,
                date,
                changedFiles
            };
        } catch (error) {
            console.error('Failed to get commit info:', error);
            throw error;
        }
    }

    public async getFileContent(filePath: string, commitHash?: string): Promise<string> {
        if (!this._gitStatus.isRepo || !this._gitStatus.repoPath) {
            throw new Error('Not in a git repository');
        }

        try {
            if (commitHash) {
                // Get content from specific commit
                const ref = `${commitHash}:${filePath}`;
                return await this.execGit(['show', ref], this._gitStatus.repoPath);
            } else {
                // Get current working directory content
                const fullPath = require('path').join(this._gitStatus.repoPath, filePath);
                const fs = require('fs');

                if (fs.existsSync(fullPath)) {
                    return fs.readFileSync(fullPath, 'utf8');
                } else {
                    // File might be deleted, try to get from HEAD
                    return await this.execGit(['show', `HEAD:${filePath}`], this._gitStatus.repoPath);
                }
            }
        } catch (error) {
            console.error('Failed to get file content:', error);
            throw error;
        }
    }

    public async getFileDiff(filePath: string, baseBranch?: string): Promise<string> {
        if (!this._gitStatus.isRepo || !this._gitStatus.repoPath) {
            throw new Error('Not in a git repository');
        }

        const base = baseBranch || this._gitStatus.baseBranch || 'HEAD~1';
        
        try {
            return await this.execGit([
                'diff', 
                base, 
                'HEAD', 
                '--', 
                filePath
            ], this._gitStatus.repoPath);
        } catch (error) {
            console.error('Failed to get file diff:', error);
            throw error;
        }
    }

    private mapGitStatus(status: string): 'added' | 'modified' | 'deleted' | 'renamed' {
        switch (status.charAt(0)) {
            case 'A': return 'added';
            case 'M': return 'modified';
            case 'D': return 'deleted';
            case 'R': return 'renamed';
            default: return 'modified';
        }
    }

    private execGit(args: string[], cwd: string): Promise<string> {
        return new Promise((resolve, reject) => {
            cp.execFile('git', args, { cwd }, (err, stdout, stderr) => {
                if (err) {
                    reject(new Error(stderr || err.message));
                } else {
                    resolve(stdout);
                }
            });
        });
    }

    public async getUnreviewedCommits(): Promise<string[]> {
        if (this._context.globalState) {
            const stored = this._context.globalState.get<string[]>('amazingly.unreviewedCommits', []);
            this._unreviewedCommits = stored;
        }
        return this._unreviewedCommits;
    }

    public async markCommitAsReviewed(commitHash: string): Promise<void> {
        this._unreviewedCommits = this._unreviewedCommits.filter(hash => hash !== commitHash);
        if (this._context.globalState) {
            await this._context.globalState.update('amazingly.unreviewedCommits', this._unreviewedCommits);
        }
    }

    public async markAllCommitsAsReviewed(): Promise<void> {
        this._unreviewedCommits = [];
        if (this._context.globalState) {
            await this._context.globalState.update('amazingly.unreviewedCommits', this._unreviewedCommits);
        }
    }

    private async addUnreviewedCommit(commitHash: string): Promise<void> {
        if (!this._unreviewedCommits.includes(commitHash)) {
            this._unreviewedCommits.push(commitHash);
            if (this._context.globalState) {
                await this._context.globalState.update('amazingly.unreviewedCommits', this._unreviewedCommits);
            }
        }
    }

    public async getCommitsSince(sinceHash?: string): Promise<CommitInfo[]> {
        if (!this._gitStatus.isRepo || !this._gitStatus.repoPath) {
            return [];
        }

        try {
            const args = ['log', '--format=%H|%s|%an|%ad', '--date=iso'];
            if (sinceHash) {
                args.push(`${sinceHash}..HEAD`);
            } else {
                args.push('-10'); // Last 10 commits
            }

            const output = await this.execGit(args, this._gitStatus.repoPath);
            const commits: CommitInfo[] = [];

            for (const line of output.split('\n')) {
                if (!line.trim()) continue;

                const [hash, message, author, date] = line.split('|');
                if (hash && message) {
                    const commitInfo = await this.getCommitInfo(hash);
                    commits.push(commitInfo);
                }
            }

            return commits;
        } catch (error) {
            console.error('Failed to get commits:', error);
            return [];
        }
    }

    public dispose(): void {
        if (this._gitWatcher) {
            this._gitWatcher.close();
        }
        if (this._updateTimeout) {
            clearTimeout(this._updateTimeout);
        }
        this._onDidChangeGitStatus.dispose();
        this._onDidCommit.dispose();
    }
}
