# Git Commit Detection Test

## Steps to Test the Extension

1. **Open VS Code with the extension loaded**
   - Press `F5` to run the extension in debug mode
   - Open the workspace folder in the new VS Code window

2. **Check Console Output**
   - Open Developer Tools: `Help > Toggle Developer Tools`
   - Go to the Console tab
   - Look for these messages:
     - "Setting up git event listeners..."
     - "Updating git status..."
     - "Found git repo at: [path]"
     - "Git watcher is ready and watching for changes"

3. **Test Git Status Command**
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
   - Type "Test Git Status (Debug)" and run it
   - Check the popup message and console output

4. **Test Commit Detection**
   - Make a small change to any file (add a comment or space)
   - Stage the file: `git add .`
   - Commit: `git commit -m "Test commit for extension"`
   - Watch the console for:
     - "Git watcher detected change: [path]"
     - "New commit detected: [hash]"
     - "Git commit detected: [commit info]"

5. **Expected Results**
   - You should see a popup notification in the bottom-right corner
   - The extension panel should update to show the new commit
   - Console should show all the debug messages

## Troubleshooting

If the commit detection doesn't work:

1. **Check if git watcher is working:**
   - Make any file change and save
   - Look for "Git watcher detected change" in console

2. **Check if git status updates:**
   - Look for "Updating git status..." messages
   - Check if "New commit detected" appears

3. **Check authentication:**
   - Make sure you're signed in with GitHub
   - Look for "User is authenticated" vs "User not authenticated" messages

4. **Manual trigger:**
   - Use the "Test Git Status" command to see current state
   - Check if `changedFiles` array has content

## Debug Console Commands

You can also run these in the VS Code console:

```javascript
// Check current git status
console.log('Git Status:', gitService.gitStatus);

// Check if user is authenticated
console.log('Authenticated:', authService.isAuthenticated);

// Manually trigger git status update
gitService.updateGitStatus();
```
