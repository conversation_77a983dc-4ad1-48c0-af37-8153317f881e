{"name": "amazingly-ext", "displayName": "Amazingly AI Code Review", "description": "AI-powered code reviews directly in your IDE with real-time feedback and branch analysis", "version": "0.2.8", "publisher": "amazingly", "repository": {"type": "git", "url": "https://github.com/amazingly/amazingly-vscode-ext.git"}, "engines": {"vscode": "^1.100.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "amazingly.signIn", "title": "Sign in with GitHub", "icon": "$(github)"}, {"command": "amazingly.signOut", "title": "Sign Out", "icon": "$(sign-out)"}, {"command": "amazingly.showWelcome", "title": "Welcome"}, {"command": "amazingly.reviewAllChanges", "title": "Review All Changes", "icon": "$(search-view-icon)"}, {"command": "amazingly.reviewFile", "title": "Review File", "icon": "$(file-code)"}, {"command": "amazingly.testGitStatus", "title": "Test Git Status (Debug)", "icon": "$(debug)"}, {"command": "amazingly.refreshGitStatus", "title": "Refresh Git Status (Debug)", "icon": "$(refresh)"}], "viewsContainers": {"activitybar": [{"id": "amazingly", "title": "Amazingly AI Review", "icon": "$(robot)"}]}, "views": {"amazingly": [{"type": "webview", "id": "amazingly<PERSON><PERSON><PERSON>", "name": "AI Code Review"}]}, "viewsWelcome": [{"view": "amazingly<PERSON><PERSON><PERSON>", "contents": "Welcome to Amazingly AI Code Review!\n[Sign in with GitHub](command:amazingly.signIn)\nAnalyze PRs with full codebase context and catch bugs instantly.", "when": "!amazingly.authenticated"}], "configuration": {"title": "Amazingly AI Code Review", "properties": {"amazingly.openRouterApiKey": {"type": "string", "default": "", "description": "OpenRouter API Key for Deepseek V3 (deepseek/deepseek-chat-v3-0324) integration", "scope": "application"}, "amazingly.autoReviewOnCommit": {"type": "boolean", "default": true, "description": "Automatically show review popup when new commits are detected"}, "amazingly.maxUnreviewedCommits": {"type": "number", "default": 5, "description": "Maximum number of unreviewed commits before showing warning"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src", "test": "vscode-test"}, "dependencies": {"axios": "^1.6.2", "chokidar": "^3.6.0"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/vscode": "^1.100.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.5.2", "eslint": "^9.25.1", "typescript": "^5.8.3"}}