import * as vscode from 'vscode';
import { AuthService } from './auth';
import { GitService, GitStatus } from './gitService';
import { AIReviewService, ReviewResult, ReviewProgress } from './aiReviewService';
import { NotificationService } from './notificationService';
import * as cp from 'child_process';

export class WelcomeViewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'amazinglyWelcome';

    private _view?: vscode.WebviewView;
    private _gitStatus: GitStatus = { isRepo: false };
    private _reviewInProgress: boolean = false;
    private _reviewResults: ReviewResult[] = [];
    private _reviewProgress: ReviewProgress = { totalFiles: 0, reviewedFiles: 0, isComplete: true };
    private _reviewLog: string[] = [];
    private _expandedFiles: Set<string> = new Set();

    constructor(
        private readonly context: vscode.ExtensionContext,
        private readonly authService: AuthService,
        private readonly gitService: GitService,
        private readonly aiReviewService: AIReviewService,
        private readonly notificationService: NotificationService
    ) {
        // Listen for AI review progress updates
        this.aiReviewService.onDidUpdateProgress((progress) => {
            this._reviewProgress = progress;
            this._reviewInProgress = !progress.isComplete;

            // Add to review log
            if (progress.currentFile) {
                this.addToReviewLog(`📄 Reviewing: ${progress.currentFile} (${progress.reviewedFiles + 1}/${progress.totalFiles})`);
            } else if (progress.isComplete) {
                this.addToReviewLog(`✅ Review completed! Analyzed ${progress.totalFiles} file(s)`);
            } else if (progress.error) {
                this.addToReviewLog(`❌ Error: ${progress.error}`);
            }

            this.updateWebview();
        });

        // Listen for AI review completion
        this.aiReviewService.onDidCompleteReview((results) => {
            this._reviewResults = results;
            this._reviewInProgress = false;

            const totalIssues = results.reduce((sum, result) => sum + result.suggestions.length, 0);
            const avgScore = results.reduce((sum, result) => sum + result.overallScore, 0) / results.length;
            this.addToReviewLog(`🎯 Review Summary: ${totalIssues} issues found, average score: ${Math.round(avgScore)}%`);

            this.updateWebview();
        });
    }

    public async resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this.context.extensionUri]
        };

        // Initial git status check
        this._gitStatus = this.gitService.gitStatus;
        webviewView.webview.html = this.getHtmlForWebview(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.type) {
                    case 'signIn':
                        await this.authService.signIn();
                        break;
                    case 'signOut':
                        await this.authService.signOut();
                        break;
                    case 'initGit':
                        await this.initGitRepo();
                        break;
                    case 'openFolder':
                        await vscode.commands.executeCommand('vscode.openFolder');
                        break;
                    case 'selectBaseBranch':
                        // This will be handled by the git service
                        break;
                    case 'reviewAll':
                        await vscode.commands.executeCommand('amazingly.reviewAllChanges');
                        break;
                    case 'reviewFile':
                        await vscode.commands.executeCommand('amazingly.reviewFile', message.file);
                        break;
                    case 'navigateToIssue':
                        await this.navigateToIssue(message.filePath, message.line);
                        break;
                    case 'toggleFileExpansion':
                        this.toggleFileExpansion(message.filePath);
                        break;
                    case 'openTutorial':
                        this.openTutorial();
                        break;
                    case 'generatePrompt':
                        this.generateAIPrompt(message.filePath);
                        break;
                }
            },
            undefined,
            this.context.subscriptions
        );

        // Update view when authentication state changes
        this.authService.onDidChangeUser(() => {
            this.updateWebview();
        });

        // Listen for git status changes
        this.gitService.onDidChangeGitStatus((gitStatus) => {
            console.log('Welcome view received git status change:', gitStatus);
            this._gitStatus = gitStatus;
            this.updateWebview();
        });

        // Listen for workspace folder changes to update git status
        vscode.workspace.onDidChangeWorkspaceFolders(() => {
            this._gitStatus = this.gitService.gitStatus;
            this.updateWebview();
        });
    }

    public updateGitStatus(gitStatus: GitStatus): void {
        this._gitStatus = gitStatus;
        this.updateWebview();
    }

    private addToReviewLog(message: string): void {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `[${timestamp}] ${message}`;
        this._reviewLog.unshift(logEntry); // Add to beginning

        // Keep only last 10 entries
        if (this._reviewLog.length > 10) {
            this._reviewLog = this._reviewLog.slice(0, 10);
        }
    }

    public startNewReview(): void {
        this._reviewLog = []; // Clear previous log
        this._reviewResults = []; // Clear previous results to show loading state
        this._reviewInProgress = true; // Set review in progress
        this.addToReviewLog('🚀 Starting new code review...');
        this.updateWebview();
    }

    public setReviewResults(results: ReviewResult[]): void {
        console.log('📊 Setting review results in welcome view:', results.length, 'results');
        this._reviewResults = results;
        this._reviewInProgress = false;

        const totalIssues = results.reduce((sum, result) => sum + result.suggestions.length, 0);
        const avgScore = results.reduce((sum, result) => sum + result.overallScore, 0) / results.length;
        this.addToReviewLog(`🎯 Review Summary: ${totalIssues} issues found, average score: ${Math.round(avgScore)}%`);

        this.updateWebview();
        console.log('✅ Welcome view updated with review results');
    }

    private async navigateToIssue(filePath: string, line?: number): Promise<void> {
        try {
            // Get the workspace folder
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders || workspaceFolders.length === 0) {
                vscode.window.showErrorMessage('No workspace folder found.');
                return;
            }

            // Construct the full file path
            const fullPath = vscode.Uri.joinPath(workspaceFolders[0].uri, filePath);

            // Open the file
            const document = await vscode.workspace.openTextDocument(fullPath);
            const editor = await vscode.window.showTextDocument(document);

            // Navigate to the specific line if provided
            if (line && line > 0) {
                const position = new vscode.Position(Math.max(0, line - 1), 0);
                editor.selection = new vscode.Selection(position, position);
                editor.revealRange(new vscode.Range(position, position), vscode.TextEditorRevealType.InCenter);
            }
        } catch (error) {
            console.error('Failed to navigate to issue:', error);
            vscode.window.showErrorMessage(`Failed to open file: ${filePath}`);
        }
    }

    private toggleFileExpansion(filePath: string): void {
        if (this._expandedFiles.has(filePath)) {
            this._expandedFiles.delete(filePath);
        } else {
            this._expandedFiles.add(filePath);
        }
        this.updateWebview();
    }

    private openTutorial(): void {
        // Create and show tutorial webview panel
        const panel = vscode.window.createWebviewPanel(
            'amazinglyTutorial',
            'Amazingly AI Code Review - Tutorial',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        panel.webview.html = this.getTutorialHtml();
    }

    private generateAIPrompt(filePath: string): void {
        // Find the review result for this file
        const fileResult = this._reviewResults.find(result => result.filePath === filePath);
        if (!fileResult || fileResult.suggestions.length === 0) {
            vscode.window.showInformationMessage('No issues found for this file to generate a prompt.');
            return;
        }

        const prompt = this.createAIPromptForFile(fileResult);

        // Create and show prompt in a new webview panel
        const panel = vscode.window.createWebviewPanel(
            'amazinglyPrompt',
            `AI Prompt - ${fileResult.filePath.split('/').pop()}`,
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        panel.webview.html = this.getPromptDisplayHtml(prompt, fileResult);
    }

    private createAIPromptForFile(fileResult: ReviewResult): string {
        const fileName = fileResult.filePath.split('/').pop() || fileResult.filePath;
        const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
        const language = this.getLanguageFromExtension(fileExtension);

        // Group issues by severity
        const highSeverityIssues = fileResult.suggestions.filter(s => s.severity === 'high');
        const mediumSeverityIssues = fileResult.suggestions.filter(s => s.severity === 'medium');
        const lowSeverityIssues = fileResult.suggestions.filter(s => s.severity === 'low');

        let prompt = `Please help me fix the following issues in my ${language} file \`${fileResult.filePath}\`:\n\n`;

        // Add file summary
        prompt += `**File Summary:**\n`;
        prompt += `- Total Issues: ${fileResult.suggestions.length}\n`;
        prompt += `- Code Quality Score: ${fileResult.overallScore}%\n`;
        if (highSeverityIssues.length > 0) prompt += `- High Severity Issues: ${highSeverityIssues.length}\n`;
        if (mediumSeverityIssues.length > 0) prompt += `- Medium Severity Issues: ${mediumSeverityIssues.length}\n`;
        if (lowSeverityIssues.length > 0) prompt += `- Low Severity Issues: ${lowSeverityIssues.length}\n`;
        prompt += `\n`;

        // Add issues by severity
        const addIssuesSection = (issues: any[], severityLabel: string) => {
            if (issues.length === 0) return;

            prompt += `## ${severityLabel} Issues:\n\n`;
            issues.forEach((issue, index) => {
                const issueNumber = index + 1;
                const lineInfo = issue.line ? ` (Line ${issue.line})` : '';
                const typeIcon = this.getIssueTypeIcon(issue.type);

                prompt += `**Issue ${issueNumber}${lineInfo} - ${typeIcon} ${issue.type.toUpperCase()}:**\n`;
                prompt += `- **Problem:** ${issue.message}\n`;
                prompt += `- **Description:** ${issue.description}\n`;
                if (issue.suggestedFix) {
                    prompt += `- **Suggested Fix:** ${issue.suggestedFix}\n`;
                }
                prompt += `\n`;
            });
        };

        addIssuesSection(highSeverityIssues, '🔴 High Severity');
        addIssuesSection(mediumSeverityIssues, '🟡 Medium Severity');
        addIssuesSection(lowSeverityIssues, '🟢 Low Severity');

        // Add instructions
        prompt += `## Instructions:\n\n`;
        prompt += `1. Please analyze each issue carefully\n`;
        prompt += `2. Provide the corrected code with all issues fixed\n`;
        prompt += `3. Explain the changes you made for each issue\n`;
        prompt += `4. Ensure the code follows best practices for ${language}\n`;
        prompt += `5. Maintain the original functionality while fixing the problems\n\n`;

        prompt += `Please provide the complete corrected code with explanations for each fix.`;

        return prompt;
    }

    private getLanguageFromExtension(extension: string): string {
        const languageMap: { [key: string]: string } = {
            'js': 'JavaScript',
            'ts': 'TypeScript',
            'jsx': 'React JSX',
            'tsx': 'React TSX',
            'py': 'Python',
            'java': 'Java',
            'cpp': 'C++',
            'c': 'C',
            'cs': 'C#',
            'php': 'PHP',
            'rb': 'Ruby',
            'go': 'Go',
            'rs': 'Rust',
            'swift': 'Swift',
            'kt': 'Kotlin',
            'scala': 'Scala',
            'html': 'HTML',
            'css': 'CSS',
            'scss': 'SCSS',
            'sass': 'Sass',
            'less': 'Less',
            'json': 'JSON',
            'xml': 'XML',
            'yaml': 'YAML',
            'yml': 'YAML',
            'md': 'Markdown',
            'sql': 'SQL',
            'sh': 'Shell Script',
            'bash': 'Bash',
            'ps1': 'PowerShell',
            'dockerfile': 'Docker',
            'vue': 'Vue.js',
            'svelte': 'Svelte'
        };

        return languageMap[extension] || 'Code';
    }

    private getIssueTypeIcon(type: string): string {
        const iconMap: { [key: string]: string } = {
            'syntax': '🔧',
            'security': '🛡️',
            'bug': '🐛',
            'performance': '⚡',
            'style': '🎨',
            'best-practice': '✨'
        };

        return iconMap[type] || '📝';
    }

    private getPromptDisplayHtml(prompt: string, fileResult: ReviewResult): string {
        const fileName = fileResult.filePath.split('/').pop() || fileResult.filePath;

        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>AI Prompt - ${fileName}</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    padding: 20px;
                    margin: 0;
                    background-color: var(--vscode-editor-background);
                    color: var(--vscode-editor-foreground);
                    line-height: 1.6;
                }
                .prompt-container {
                    max-width: 900px;
                    margin: 0 auto;
                }
                .prompt-header {
                    background: var(--vscode-sideBar-background);
                    border-radius: 8px;
                    padding: 20px;
                    margin-bottom: 20px;
                    border-left: 4px solid var(--vscode-button-background);
                }
                .prompt-title {
                    font-size: 20px;
                    font-weight: 600;
                    margin-bottom: 8px;
                    color: var(--vscode-button-background);
                }
                .prompt-subtitle {
                    color: var(--vscode-descriptionForeground);
                    font-size: 14px;
                }
                .prompt-content {
                    background: var(--vscode-input-background);
                    border: 1px solid var(--vscode-input-border);
                    border-radius: 6px;
                    padding: 20px;
                    margin-bottom: 20px;
                    font-family: monospace;
                    font-size: 13px;
                    line-height: 1.5;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                    max-height: 70vh;
                    overflow-y: auto;
                }
                .action-buttons {
                    display: flex;
                    gap: 12px;
                    margin-bottom: 20px;
                }
                .btn {
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: background 0.2s;
                }
                .btn:hover {
                    background: var(--vscode-button-hoverBackground);
                }
                .btn-secondary {
                    background: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                    border: 1px solid var(--vscode-button-border);
                }
                .btn-secondary:hover {
                    background: var(--vscode-button-secondaryHoverBackground);
                }
                .copy-success {
                    color: var(--vscode-testing-iconPassed);
                    font-size: 12px;
                    margin-left: 8px;
                    opacity: 0;
                    transition: opacity 0.3s;
                }
                .copy-success.show {
                    opacity: 1;
                }
                .usage-tips {
                    background: var(--vscode-inputValidation-infoBackground);
                    border: 1px solid var(--vscode-inputValidation-infoBorder);
                    border-radius: 6px;
                    padding: 16px;
                    margin-top: 20px;
                }
                .usage-tips h3 {
                    margin-top: 0;
                    margin-bottom: 12px;
                    font-size: 16px;
                }
                .usage-tips ul {
                    margin: 0;
                    padding-left: 20px;
                }
                .usage-tips li {
                    margin-bottom: 6px;
                    font-size: 13px;
                }
            </style>
        </head>
        <body>
            <div class="prompt-container">
                <div class="prompt-header">
                    <div class="prompt-title">🤖 AI Code Fix Prompt</div>
                    <div class="prompt-subtitle">Generated for: ${fileResult.filePath}</div>
                </div>

                <div class="action-buttons">
                    <button class="btn" onclick="copyToClipboard()">
                        📋 Copy to Clipboard
                    </button>
                    <button class="btn-secondary btn" onclick="selectAll()">
                        📝 Select All
                    </button>
                    <span class="copy-success" id="copySuccess">✅ Copied to clipboard!</span>
                </div>

                <div class="prompt-content" id="promptContent">${this.escapeHtml(prompt)}</div>

                <div class="usage-tips">
                    <h3>💡 How to Use This Prompt:</h3>
                    <ul>
                        <li><strong>Copy the prompt above</strong> and paste it into your AI code editor</li>
                        <li><strong>Supported AI Editors:</strong> GitHub Copilot Chat, Cursor, Continue.dev, Claude, ChatGPT</li>
                        <li><strong>Include your current code</strong> when pasting to the AI for better context</li>
                        <li><strong>Review AI suggestions</strong> carefully before applying changes</li>
                        <li><strong>Test the fixed code</strong> to ensure functionality is maintained</li>
                    </ul>
                </div>
            </div>

            <script>
                function copyToClipboard() {
                    const content = document.getElementById('promptContent').textContent;
                    navigator.clipboard.writeText(content).then(() => {
                        const success = document.getElementById('copySuccess');
                        success.classList.add('show');
                        setTimeout(() => {
                            success.classList.remove('show');
                        }, 2000);
                    }).catch(err => {
                        console.error('Failed to copy: ', err);
                        // Fallback for older browsers
                        selectAll();
                    });
                }

                function selectAll() {
                    const content = document.getElementById('promptContent');
                    const range = document.createRange();
                    range.selectNodeContents(content);
                    const selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);
                }
            </script>
        </body>
        </html>`;
    }

    private escapeHtml(text: string): string {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    private getTutorialHtml(): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Amazingly AI Code Review - Tutorial</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    padding: 20px;
                    margin: 0;
                    background-color: var(--vscode-editor-background);
                    color: var(--vscode-editor-foreground);
                    line-height: 1.6;
                }
                .tutorial-container {
                    max-width: 800px;
                    margin: 0 auto;
                }
                .tutorial-header {
                    text-align: center;
                    margin-bottom: 40px;
                    padding: 20px;
                    background: var(--vscode-sideBar-background);
                    border-radius: 12px;
                }
                .tutorial-title {
                    font-size: 28px;
                    font-weight: 600;
                    margin-bottom: 8px;
                    color: var(--vscode-button-background);
                }
                .tutorial-subtitle {
                    font-size: 16px;
                    color: var(--vscode-descriptionForeground);
                }
                .step-section {
                    background: var(--vscode-editorWidget-background);
                    border-radius: 8px;
                    padding: 24px;
                    margin-bottom: 24px;
                    border-left: 4px solid var(--vscode-button-background);
                }
                .step-number {
                    display: inline-block;
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 32px;
                    font-weight: 600;
                    margin-right: 12px;
                }
                .step-title {
                    font-size: 20px;
                    font-weight: 600;
                    margin-bottom: 12px;
                    display: flex;
                    align-items: center;
                }
                .step-description {
                    margin-bottom: 16px;
                    color: var(--vscode-descriptionForeground);
                }
                .step-actions {
                    background: var(--vscode-input-background);
                    border: 1px solid var(--vscode-input-border);
                    border-radius: 6px;
                    padding: 16px;
                    margin: 12px 0;
                }
                .action-item {
                    display: flex;
                    align-items: center;
                    margin-bottom: 8px;
                    font-family: monospace;
                    font-size: 14px;
                }
                .action-icon {
                    margin-right: 8px;
                    font-size: 16px;
                }
                .code-snippet {
                    background: var(--vscode-textCodeBlock-background);
                    border: 1px solid var(--vscode-input-border);
                    border-radius: 4px;
                    padding: 12px;
                    font-family: monospace;
                    font-size: 13px;
                    margin: 8px 0;
                    overflow-x: auto;
                }
                .highlight-box {
                    background: var(--vscode-inputValidation-infoBackground);
                    border: 1px solid var(--vscode-inputValidation-infoBorder);
                    border-radius: 6px;
                    padding: 12px;
                    margin: 12px 0;
                }
                .feature-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 16px;
                    margin: 20px 0;
                }
                .feature-card {
                    background: var(--vscode-sideBar-background);
                    border-radius: 8px;
                    padding: 16px;
                    text-align: center;
                }
                .feature-icon {
                    font-size: 32px;
                    margin-bottom: 8px;
                }
                .feature-title {
                    font-weight: 600;
                    margin-bottom: 8px;
                }
                .feature-desc {
                    font-size: 13px;
                    color: var(--vscode-descriptionForeground);
                }
            </style>
        </head>
        <body>
            <div class="tutorial-container">
                <div class="tutorial-header">
                    <div class="tutorial-title">🤖 Amazingly AI Code Review</div>
                    <div class="tutorial-subtitle">Complete Guide to AI-Powered Code Analysis with Real-time Progress & AI Prompt Generation</div>
                </div>

                <div class="step-section">
                    <div class="step-title">
                        <span class="step-number">1</span>
                        GitHub Authentication
                    </div>
                    <div class="step-description">
                        Start by connecting your GitHub account to enable repository access and commit tracking.
                    </div>
                    <div class="step-actions">
                        <div class="action-item">
                            <span class="action-icon">🔐</span>
                            Click "Sign in with GitHub" in the extension panel
                        </div>
                        <div class="action-item">
                            <span class="action-icon">✅</span>
                            Authorize Amazingly in your browser
                        </div>
                        <div class="action-item">
                            <span class="action-icon">👤</span>
                            Your profile will appear in the dashboard
                        </div>
                    </div>
                    <div class="highlight-box">
                        <strong>💡 Pro Tip:</strong> Your GitHub token is securely stored using VS Code's SecretStorage API.
                    </div>
                </div>

                <div class="step-section">
                    <div class="step-title">
                        <span class="step-number">2</span>
                        Initialize Git Repository
                    </div>
                    <div class="step-description">
                        Set up a Git repository in your workspace to enable version control and change tracking.
                    </div>
                    <div class="step-actions">
                        <div class="action-item">
                            <span class="action-icon">📁</span>
                            Open a folder in VS Code
                        </div>
                        <div class="action-item">
                            <span class="action-icon">🔧</span>
                            Click "Initialize Git Repository" if not already initialized
                        </div>
                        <div class="action-item">
                            <span class="action-icon">🌿</span>
                            Create branches for feature development
                        </div>
                    </div>
                    <div class="code-snippet">
                        # Alternative: Initialize via terminal<br>
                        git init<br>
                        git add .<br>
                        git commit -m "Initial commit"
                    </div>
                </div>

                <div class="step-section">
                    <div class="step-title">
                        <span class="step-number">3</span>
                        Make Code Changes
                    </div>
                    <div class="step-description">
                        Write or modify your code. Amazingly works with all programming languages and file types.
                    </div>
                    <div class="step-actions">
                        <div class="action-item">
                            <span class="action-icon">✏️</span>
                            Edit files in your workspace
                        </div>
                        <div class="action-item">
                            <span class="action-icon">💾</span>
                            Save your changes
                        </div>
                        <div class="action-item">
                            <span class="action-icon">👀</span>
                            Changes will appear in the "Branch & File Review" section
                        </div>
                    </div>
                    <div class="highlight-box">
                        <strong>🎯 Best Practice:</strong> Make focused, logical changes that can be easily reviewed.
                    </div>
                </div>

                <div class="step-section">
                    <div class="step-title">
                        <span class="step-number">4</span>
                        Commit Your Changes
                    </div>
                    <div class="step-description">
                        Commit your changes to trigger automatic AI review notifications.
                    </div>
                    <div class="step-actions">
                        <div class="action-item">
                            <span class="action-icon">📝</span>
                            Stage your changes (git add)
                        </div>
                        <div class="action-item">
                            <span class="action-icon">💬</span>
                            Write a descriptive commit message
                        </div>
                        <div class="action-item">
                            <span class="action-icon">🚀</span>
                            Commit the changes (git commit)
                        </div>
                    </div>
                    <div class="code-snippet">
                        git add .<br>
                        git commit -m "Add new feature: user authentication"
                    </div>
                </div>

                <div class="step-section">
                    <div class="step-title">
                        <span class="step-number">5</span>
                        AI Review Process
                    </div>
                    <div class="step-description">
                        After committing, Amazingly automatically detects changes and offers AI-powered code review with real-time progress tracking.
                    </div>
                    <div class="step-actions">
                        <div class="action-item">
                            <span class="action-icon">🔔</span>
                            Popup notification appears in bottom-right
                        </div>
                        <div class="action-item">
                            <span class="action-icon">🤖</span>
                            Click "Review Changes" to start AI analysis
                        </div>
                        <div class="action-item">
                            <span class="action-icon">⏳</span>
                            Watch animated progress bars and loading spinners
                        </div>
                        <div class="action-item">
                            <span class="action-icon">📊</span>
                            Monitor file-by-file progress in "Review Progress & History"
                        </div>
                        <div class="action-item">
                            <span class="action-icon">💭</span>
                            "Inline AI Suggestions" shows waiting message during analysis
                        </div>
                        <div class="action-item">
                            <span class="action-icon">🎯</span>
                            Review results appear automatically when complete
                        </div>
                    </div>
                    <div class="highlight-box">
                        <strong>✨ New Feature:</strong> Beautiful loading animations keep you informed throughout the entire review process!
                    </div>
                </div>

                <div class="step-section">
                    <div class="step-title">
                        <span class="step-number">6</span>
                        Review Results & Navigation
                    </div>
                    <div class="step-description">
                        Analyze AI-generated suggestions and navigate directly to issues in your code.
                    </div>
                    <div class="step-actions">
                        <div class="action-item">
                            <span class="action-icon">🔍</span>
                            Click on any issue to jump to the exact line
                        </div>
                        <div class="action-item">
                            <span class="action-icon">📋</span>
                            Expand files to see all detected issues
                        </div>
                        <div class="action-item">
                            <span class="action-icon">⚡</span>
                            Apply suggested fixes to improve code quality
                        </div>
                        <div class="action-item">
                            <span class="action-icon">📊</span>
                            Monitor progress with real-time loading animations
                        </div>
                    </div>
                </div>

                <div class="step-section">
                    <div class="step-title">
                        <span class="step-number">7</span>
                        Generate AI Prompts for Quick Fixes
                    </div>
                    <div class="step-description">
                        Generate comprehensive prompts for each file that you can copy and paste into any AI code editor for instant fixes.
                    </div>
                    <div class="step-actions">
                        <div class="action-item">
                            <span class="action-icon">📋</span>
                            Click "Generate AI Prompt" button next to any file
                        </div>
                        <div class="action-item">
                            <span class="action-icon">📄</span>
                            View detailed prompt with all issues organized by severity
                        </div>
                        <div class="action-item">
                            <span class="action-icon">📋</span>
                            Copy prompt to clipboard with one click
                        </div>
                        <div class="action-item">
                            <span class="action-icon">🤖</span>
                            Paste into AI code editors (Cursor, GitHub Copilot, Claude, etc.)
                        </div>
                        <div class="action-item">
                            <span class="action-icon">✨</span>
                            Get instant code fixes with detailed explanations
                        </div>
                    </div>
                    <div class="highlight-box">
                        <strong>🎯 Pro Tip:</strong> The generated prompts include file context, issue severity, line numbers, and specific fix suggestions - perfect for AI assistants!
                    </div>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🛡️</div>
                        <div class="feature-title">Security Analysis</div>
                        <div class="feature-desc">Detects security vulnerabilities and potential exploits</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🐛</div>
                        <div class="feature-title">Bug Detection</div>
                        <div class="feature-desc">Identifies logical errors and potential runtime issues</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <div class="feature-title">Performance</div>
                        <div class="feature-desc">Suggests optimizations for better performance</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎨</div>
                        <div class="feature-title">Code Style</div>
                        <div class="feature-desc">Enforces consistent coding standards</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">✨</div>
                        <div class="feature-title">Best Practices</div>
                        <div class="feature-desc">Recommends industry-standard patterns</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔧</div>
                        <div class="feature-title">Syntax Checking</div>
                        <div class="feature-desc">Catches syntax errors and typos</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📋</div>
                        <div class="feature-title">AI Prompt Generation</div>
                        <div class="feature-desc">Generate ready-to-use prompts for AI code editors</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⏳</div>
                        <div class="feature-title">Real-time Progress</div>
                        <div class="feature-desc">Beautiful loading animations and progress tracking</div>
                    </div>
                </div>

                <div class="highlight-box" style="text-align: center; margin-top: 40px;">
                    <strong>🎉 You're all set!</strong><br>
                    Start coding and let Amazingly AI help you write better, safer code.<br><br>
                    <strong>🚀 Latest Features:</strong><br>
                    • Real-time progress tracking with beautiful animations<br>
                    • AI prompt generation for instant code fixes<br>
                    • Enhanced user experience with loading states<br>
                    • Support for 25+ programming languages
                </div>
            </div>
        </body>
        </html>`;
    }

    private async initGitRepo() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders && workspaceFolders.length > 0) {
            try {
                console.log('Initializing git repository...');

                // Use child_process to run git init synchronously
                const workspaceRoot = workspaceFolders[0].uri.fsPath;
                await new Promise<void>((resolve, reject) => {
                    cp.exec('git init', { cwd: workspaceRoot }, (error, stdout, stderr) => {
                        if (error) {
                            console.error('Git init failed:', error);
                            reject(error);
                        } else {
                            console.log('Git init successful:', stdout);
                            resolve();
                        }
                    });
                });

                vscode.window.showInformationMessage('Initialized a new Git repository.');

                // Force git service to refresh after a short delay
                setTimeout(async () => {
                    console.log('Refreshing git status after initialization...');
                    await this.gitService.refreshGitStatus();
                }, 1000);

            } catch (error) {
                console.error('Failed to initialize git repository:', error);
                vscode.window.showErrorMessage('Failed to initialize Git repository.');
            }
        }
    }

    private getHtmlForWebview(webview: vscode.Webview): string {
        const isAuthenticated = this.authService.isAuthenticated;
        const user = this.authService.user;
        if (isAuthenticated && user) {
            return this.getAuthenticatedView(user);
        } else {
            return this.getWelcomeView();
        }
    }

    private getAuthenticatedView(user: any): string {
        // Git repo status UI
        let gitStatusHtml = '';
        if (this._gitStatus.isRepo) {
            gitStatusHtml = `
                <span style=\"color: #4caf50;\">●</span> Git repository detected at <code>${this._gitStatus.repoPath}</code>.<br>
                <span style=\"color: var(--vscode-descriptionForeground); font-size: 13px;\">You can now use all code review features.</span>
            `;
        } else {
            gitStatusHtml = `
                <span style=\"color: #ff9800;\">●</span> <b>No Git repository found in this workspace.</b><br>
                <button class=\"git-btn\" onclick=\"initGit()\">Initialize Git</button>
                <button class=\"git-btn\" onclick=\"openFolder()\">Open Folder...</button>
                <div style=\"color: var(--vscode-descriptionForeground); font-size: 13px; margin-top: 6px;\">Amazingly works best with Git repositories.</div>
            `;
        }
        // Branch & file review UI
        let branchReviewHtml = '';
        if (this._gitStatus.isRepo) {
            const branches = this._gitStatus.branches || [];
            const activeBranch = this._gitStatus.activeBranch || '';
            const baseBranch = this._gitStatus.baseBranch || '';
            const changedFiles = this._gitStatus.changedFiles || [];

            if (branches.length <= 1) {
                branchReviewHtml = `
                    <div style=\"margin-bottom: 8px;\">
                        <b>Active branch:</b> <code>${activeBranch}</code><br>
                        <b>Base branch:</b> <span style=\"color: var(--vscode-descriptionForeground);\">Only one branch found. Create another branch to compare.</span>
                    </div>
                    <div style=\"margin-bottom: 8px;\">
                        <button class=\"git-btn\" id=\"reviewAllBtn\" disabled title=\"No changes to review\">Review All Changes</button>
                    </div>
                    <div><b>Changed files:</b></div>
                    <div style=\"color: var(--vscode-descriptionForeground); font-size: 13px;\">No changes between branches.</div>
                `;
            } else {
                const branchOptions = branches.map(b => `<option value=\"${b}\"${b === baseBranch ? ' selected' : ''}>${b}</option>`).join('');
                const filesList = changedFiles.length > 0
                    ? `<ul style=\"margin: 8px 0 0 0; padding-left: 18px;\">${changedFiles.map(f => `<li>${f} <button class='git-btn' onclick='reviewFile(\"${f}\")'>Review</button></li>`).join('')}</ul>`
                    : '<div style=\"color: var(--vscode-descriptionForeground); font-size: 13px;\">No changes between branches.</div>';

                let progressHtml = '';
                if (this._reviewInProgress) {
                    const progress = Math.round((this._reviewProgress.reviewedFiles / this._reviewProgress.totalFiles) * 100);
                    progressHtml = `<div style=\"margin-top:10px;color:#4caf50;\">🤖 Analyzing ${this._reviewProgress.currentFile || 'changes'}... (${progress}%)</div>`;
                }

                branchReviewHtml = `
                    <div style=\"margin-bottom: 8px;\">
                        <b>Active branch:</b> <code>${activeBranch}</code><br>
                        <b>Base branch:</b> <select id=\"baseBranchSelect\" class=\"vsc-select\">${branchOptions}</select>
                    </div>
                    <div style=\"margin-bottom: 8px;\">
                        <button class=\"git-btn review-all-btn\" id=\"reviewAllBtn\"${changedFiles.length === 0 ? ' disabled title=\"No changes to review\"' : ''}>Review All Changes</button>
                    </div>
                    <div><b>Changed files:</b></div>
                    ${filesList}
                    ${progressHtml}
                `;
            }
        } else {
            branchReviewHtml = '<div style=\"color: var(--vscode-descriptionForeground); font-size: 13px;\">Initialize a Git repository to enable branch and file review.</div>';
        }
        return `<!DOCTYPE html>
        <html lang=\"en\">
        <head>
            <meta charset=\"UTF-8\">
            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
            <title>Amazingly Dashboard</title>
            <style>
                body { font-family: var(--vscode-font-family); padding: 0; margin: 0; background-color: var(--vscode-editor-background); color: var(--vscode-editor-foreground); min-height: 100vh; }
                .dashboard-container { display: flex; flex-direction: column; align-items: center; padding: 32px 16px 16px 16px; }
                .profile-card { display: flex; flex-direction: column; align-items: center; background: var(--vscode-sideBar-background); border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); padding: 24px 20px 20px 20px; margin-bottom: 32px; width: 100%; max-width: 340px; }
                .avatar { width: 72px; height: 72px; border-radius: 50%; margin-bottom: 12px; border: 2px solid var(--vscode-button-background); }
                .user-name { font-size: 20px; font-weight: 600; margin-bottom: 2px; text-align: center; }
                .user-login { color: var(--vscode-descriptionForeground); font-size: 14px; margin-bottom: 8px; text-align: center; }
                .logout-btn { background: var(--vscode-button-background); color: var(--vscode-button-foreground); border: none; border-radius: 6px; padding: 8px 18px; font-size: 14px; font-weight: 500; cursor: pointer; margin-top: 8px; transition: background 0.2s; }
                .logout-btn:hover { background: var(--vscode-button-hoverBackground); }
                .dashboard-section { width: 100%; max-width: 340px; background: var(--vscode-editorWidget-background); border-radius: 10px; padding: 18px 16px; box-shadow: 0 1px 4px rgba(0,0,0,0.04); margin-bottom: 18px; }
                .dashboard-title { font-size: 16px; font-weight: 600; margin-bottom: 10px; }
                .dashboard-placeholder { color: var(--vscode-descriptionForeground); font-size: 13px; }
                .git-btn { background: var(--vscode-button-background); color: var(--vscode-button-foreground); border: none; border-radius: 6px; padding: 6px 16px; font-size: 13px; font-weight: 500; cursor: pointer; margin: 8px 8px 0 0; transition: background 0.2s; }
                .git-btn:hover { background: var(--vscode-button-hoverBackground); }
                .review-all-btn { background: #0078d4; color: #fff; font-weight: 600; border-radius: 6px; padding: 8px 18px; font-size: 14px; margin-top: 4px; }
                .review-all-btn:disabled { background: #444; color: #bbb; cursor: not-allowed; }
                .vsc-select { background: var(--vscode-input-background); color: var(--vscode-input-foreground); border: 1px solid var(--vscode-input-border); border-radius: 4px; padding: 2px 8px; font-size: 13px; }
                .section-divider { height: 1px; background: var(--vscode-sideBarSectionHeader-border); margin: 18px 0; width: 100%; max-width: 340px; }

                /* Loading animations */
                .loading-spinner {
                    display: inline-block;
                    width: 16px;
                    height: 16px;
                    border: 2px solid var(--vscode-descriptionForeground);
                    border-radius: 50%;
                    border-top-color: var(--vscode-button-background);
                    animation: spin 1s ease-in-out infinite;
                    margin-right: 8px;
                }

                .progress-bar {
                    width: 100%;
                    height: 6px;
                    background: var(--vscode-input-background);
                    border-radius: 3px;
                    overflow: hidden;
                    margin: 8px 0;
                }

                .progress-fill {
                    height: 100%;
                    background: linear-gradient(90deg, #007ACC, #005a9e);
                    border-radius: 3px;
                    transition: width 0.3s ease;
                    animation: pulse 2s ease-in-out infinite;
                }

                .review-status {
                    display: flex;
                    align-items: center;
                    padding: 12px;
                    background: var(--vscode-input-background);
                    border-radius: 6px;
                    margin: 8px 0;
                    border-left: 3px solid var(--vscode-button-background);
                }

                .pulsing-text {
                    animation: textPulse 1.5s ease-in-out infinite;
                }

                @keyframes spin {
                    to { transform: rotate(360deg); }
                }

                @keyframes pulse {
                    0%, 100% { opacity: 1; }
                    50% { opacity: 0.7; }
                }

                @keyframes textPulse {
                    0%, 100% { opacity: 1; }
                    50% { opacity: 0.6; }
                }

                /* Guidelines styling */
                .guidelines-box {
                    background: var(--vscode-input-background);
                    border: 1px solid var(--vscode-input-border);
                    border-radius: 6px;
                    padding: 12px;
                    margin-bottom: 12px;
                    font-size: 13px;
                }

                .guidelines-steps {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 4px;
                    margin: 6px 0;
                }

                .step-badge {
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 11px;
                    font-weight: 500;
                }

                .tutorial-btn {
                    background: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                    border: 1px solid var(--vscode-button-border);
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-size: 12px;
                    cursor: pointer;
                    margin-top: 8px;
                    transition: background 0.2s;
                }

                .tutorial-btn:hover {
                    background: var(--vscode-button-secondaryHoverBackground);
                }
            </style>
        </head>
        <body>
            <div class=\"dashboard-container\">
                <div class=\"profile-card\">
                    <img class=\"avatar\" src=\"${user.avatar_url}\" alt=\"${user.login}\" />
                    <div class=\"user-name\">${user.name || user.login}</div>
                    <div class=\"user-login\">@${user.login}</div>
                    <button class=\"logout-btn\" onclick=\"signOut()\">Sign Out</button>
                </div>
                <div class=\"dashboard-section\">
                    <div class=\"dashboard-title\">Git Repository Status</div>
                    <div class=\"dashboard-placeholder\">${gitStatusHtml}</div>
                </div>
                <div class=\"dashboard-section\">
                    <div class=\"dashboard-title\">How to Use Amazingly</div>
                    <div class=\"guidelines-box\">
                        <div style=\"font-weight: 500; margin-bottom: 6px;\">🚀 Quick Start Guide:</div>
                        <div class=\"guidelines-steps\">
                            <span class=\"step-badge\">1. Login</span>
                            <span class=\"step-badge\">2. Init Git</span>
                            <span class=\"step-badge\">3. Make Changes</span>
                            <span class=\"step-badge\">4. Commit</span>
                            <span class=\"step-badge\">5. Review</span>
                        </div>
                        <div style=\"color: var(--vscode-descriptionForeground); font-size: 12px; margin-top: 6px;\">
                            Make code changes, commit them, and get instant AI-powered reviews with issue detection.
                        </div>
                        <button class=\"tutorial-btn\" onclick=\"openTutorial()\">📖 Learn How to Use This Plugin</button>
                    </div>
                </div>
                <div class=\"dashboard-section\">
                    <div class=\"dashboard-title\">Branch & File Review</div>
                    <div class=\"dashboard-placeholder\">${branchReviewHtml}</div>
                </div>
                <div class=\"dashboard-section\">
                    <div class=\"dashboard-title\">Review Progress & History</div>
                    <div class=\"dashboard-placeholder\">${this.getReviewLogHtml()}</div>
                </div>
                <div class=\"dashboard-section\">
                    <div class=\"dashboard-title\">Inline AI Suggestions</div>
                    <div class=\"dashboard-placeholder\">${this.getReviewResultsHtml()}</div>
                </div>
                <div class=\"dashboard-section\">
                    <div class=\"dashboard-title\">Security & Privacy</div>
                    <div class=\"dashboard-placeholder\">🔒 No code is stored permanently.<br>All data is transmitted securely.<br>Tokens are stored using VS Code SecretStorage.</div>
                </div>
            </div>
            <script>
                const vscode = acquireVsCodeApi();
                function signOut() { vscode.postMessage({ type: 'signOut' }); }
                function initGit() { vscode.postMessage({ type: 'initGit' }); }
                function openFolder() { vscode.postMessage({ type: 'openFolder' }); }
                window.addEventListener('DOMContentLoaded', () => {
                    const baseBranchSelect = document.getElementById('baseBranchSelect');
                    if (baseBranchSelect) {
                        baseBranchSelect.addEventListener('change', (e) => {
                            vscode.postMessage({ type: 'selectBaseBranch', branch: e.target.value });
                        });
                    }
                    const reviewAllBtn = document.getElementById('reviewAllBtn');
                    if (reviewAllBtn) {
                        reviewAllBtn.addEventListener('click', () => {
                            vscode.postMessage({ type: 'reviewAll' });
                        });
                    }
                });
                function reviewFile(file) { vscode.postMessage({ type: 'reviewFile', file }); }
                function navigateToIssue(filePath, line) {
                    vscode.postMessage({ type: 'navigateToIssue', filePath: filePath, line: line });
                }
                function toggleFileExpansion(filePath) {
                    vscode.postMessage({ type: 'toggleFileExpansion', filePath: filePath });
                }
                function openTutorial() {
                    vscode.postMessage({ type: 'openTutorial' });
                }
                function generatePrompt(filePath) {
                    vscode.postMessage({ type: 'generatePrompt', filePath: filePath });
                }
            </script>
        </body>
        </html>`;
    }

    private getWelcomeView(): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to Amazingly</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    padding: 20px;
                    margin: 0;
                    background-color: var(--vscode-editor-background);
                    color: var(--vscode-editor-foreground);
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    min-height: 100vh;
                }
                
                .logo {
                    width: 64px;
                    height: 64px;
                    background: linear-gradient(135deg, #007ACC, #005a9e);
                    border-radius: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 32px;
                    color: white;
                    margin-bottom: 24px;
                    box-shadow: 0 4px 12px rgba(0, 122, 204, 0.3);
                }
                
                .title {
                    font-size: 28px;
                    font-weight: 600;
                    margin-bottom: 8px;
                    text-align: center;
                    color: var(--vscode-editor-foreground);
                }
                
                .subtitle {
                    font-size: 16px;
                    color: var(--vscode-descriptionForeground);
                    margin-bottom: 32px;
                    text-align: center;
                    line-height: 1.5;
                }
                
                .benefits {
                    list-style: none;
                    padding: 0;
                    margin: 0 0 32px 0;
                    width: 100%;
                    max-width: 320px;
                }
                
                .benefit {
                    display: flex;
                    align-items: center;
                    margin-bottom: 16px;
                    padding: 12px;
                    background-color: var(--vscode-list-hoverBackground);
                    border-radius: 8px;
                    border-left: 3px solid var(--vscode-button-background);
                }
                
                .benefit-icon {
                    width: 20px;
                    height: 20px;
                    margin-right: 12px;
                    color: var(--vscode-button-background);
                    font-weight: bold;
                }
                
                .benefit-text {
                    flex: 1;
                    font-size: 14px;
                    line-height: 1.4;
                }
                
                .sign-in-button {
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    padding: 12px 24px;
                    font-size: 14px;
                    font-weight: 600;
                    border-radius: 6px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 16px;
                    transition: background-color 0.2s;
                    min-width: 180px;
                    justify-content: center;
                }
                
                .sign-in-button:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }
                
                .fallback-text {
                    font-size: 12px;
                    color: var(--vscode-descriptionForeground);
                    text-align: center;
                    margin-top: 16px;
                }
                
                .fallback-link {
                    color: var(--vscode-textLink-foreground);
                    text-decoration: underline;
                    cursor: pointer;
                }
                
                .github-icon {
                    width: 16px;
                    height: 16px;
                }
            </style>
        </head>
        <body>
            <div class="logo">🤖</div>
            <h1 class="title">Welcome to Amazingly</h1>
            <p class="subtitle">AI-powered code reviews directly in your IDE</p>
            
            <ul class="benefits">
                <li class="benefit">
                    <span class="benefit-icon">📊</span>
                    <span class="benefit-text">Analyze PRs with full codebase context</span>
                </li>
                <li class="benefit">
                    <span class="benefit-icon">🔍</span>
                    <span class="benefit-text">Catch bugs and security issues instantly</span>
                </li>
                <li class="benefit">
                    <span class="benefit-icon">⚡</span>
                    <span class="benefit-text">Get real-time feedback while coding</span>
                </li>
                <li class="benefit">
                    <span class="benefit-icon">🛠️</span>
                    <span class="benefit-text">Apply one-click fixes and suggestions</span>
                </li>
            </ul>
            
            <button class="sign-in-button" onclick="signIn()">
                <svg class="github-icon" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 **********.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 **********.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .***********.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"/>
                </svg>
                Sign in with GitHub
            </button>
            
            <div class="fallback-text">
                After clicking "Sign in with GitHub", complete the authentication in your browser.<br>
                You will be redirected back to VS Code automatically.
            </div>

            <script>
                const vscode = acquireVsCodeApi();
                
                function signIn() {
                    vscode.postMessage({ type: 'signIn' });
                }
            </script>
        </body>
        </html>`;
    }

    private getReviewResultsHtml(): string {
        // Show loading state when review is in progress
        if (this._reviewInProgress) {
            return `
                <div style="display: flex; align-items: center; padding: 16px; background: var(--vscode-input-background); border-radius: 6px; border-left: 3px solid var(--vscode-button-background);">
                    <div class="loading-spinner" style="margin-right: 12px;"></div>
                    <div>
                        <div class="pulsing-text" style="font-weight: 500; margin-bottom: 4px;">🤖 Analyzing Code...</div>
                        <div style="font-size: 12px; color: var(--vscode-descriptionForeground);">
                            Please wait while AI reviews your code. Results will appear here once the analysis is complete.
                        </div>
                    </div>
                </div>
            `;
        }

        // Show default message when no results and not in progress
        if (this._reviewResults.length === 0) {
            return '💡 Inline suggestions and review panel will be shown here.';
        }

        const totalIssues = this._reviewResults.reduce((sum, result) => sum + result.suggestions.length, 0);
        const avgScore = this._reviewResults.reduce((sum, result) => sum + result.overallScore, 0) / this._reviewResults.length;

        if (totalIssues === 0) {
            return `✅ <b>Review Complete!</b><br>No issues found. Code quality score: ${Math.round(avgScore)}%`;
        }

        const highSeverityIssues = this._reviewResults.reduce((sum, result) =>
            sum + result.suggestions.filter(s => s.severity === 'high').length, 0);

        const mediumSeverityIssues = this._reviewResults.reduce((sum, result) =>
            sum + result.suggestions.filter(s => s.severity === 'medium').length, 0);

        const lowSeverityIssues = this._reviewResults.reduce((sum, result) =>
            sum + result.suggestions.filter(s => s.severity === 'low').length, 0);

        let issuesSummary = '';
        if (highSeverityIssues > 0) issuesSummary += `🔴 ${highSeverityIssues} high `;
        if (mediumSeverityIssues > 0) issuesSummary += `🟡 ${mediumSeverityIssues} medium `;
        if (lowSeverityIssues > 0) issuesSummary += `🟢 ${lowSeverityIssues} low `;

        const recentResults = this._reviewResults.slice(-3); // Show last 3 files
        const filesList = recentResults.map(result => {
            const fileName = result.filePath.split('/').pop() || result.filePath;
            const issueCount = result.suggestions.length;
            const scoreColor = result.overallScore >= 80 ? '#4caf50' : result.overallScore >= 60 ? '#ff9800' : '#f44336';

            const isExpanded = this._expandedFiles.has(result.filePath);
            const expandIcon = isExpanded ? '▼' : '▶';

            // Show first 3 issues or all if expanded
            const issuesToShow = isExpanded ? result.suggestions : result.suggestions.slice(0, 3);
            const issuesList = issuesToShow.map(suggestion => {
                const severityIcon = suggestion.severity === 'high' ? '🔴' : suggestion.severity === 'medium' ? '🟡' : '🟢';
                const lineInfo = suggestion.line ? ` (line ${suggestion.line})` : '';
                return `<div style="margin-left: 16px; margin-bottom: 6px; cursor: pointer; color: var(--vscode-textLink-foreground); padding: 6px; border-radius: 4px; background: var(--vscode-editor-background); border-left: 3px solid ${suggestion.severity === 'high' ? '#f44336' : suggestion.severity === 'medium' ? '#ff9800' : '#4caf50'};"
                            onclick="navigateToIssue('${result.filePath}', ${suggestion.line || 0})"
                            title="Click to navigate to line ${suggestion.line || 'N/A'}">
                            <div style="font-weight: 500; margin-bottom: 2px;">${severityIcon} ${suggestion.message}${lineInfo}</div>
                            <div style="font-size: 11px; color: var(--vscode-descriptionForeground); margin-bottom: 2px;">${suggestion.description}</div>
                            ${suggestion.suggestedFix ? `<div style="font-size: 11px; color: var(--vscode-textLink-foreground); font-style: italic;">💡 ${suggestion.suggestedFix}</div>` : ''}
                        </div>`;
            }).join('');

            const expandButton = result.suggestions.length > 3 ?
                `<div style="margin-left: 16px; margin-top: 4px;">
                    <span style="color: var(--vscode-textLink-foreground); cursor: pointer; padding: 4px 8px; background: var(--vscode-button-secondaryBackground); border-radius: 4px; display: inline-block; font-size: 12px; border: 1px solid var(--vscode-button-border);"
                          onclick="toggleFileExpansion('${result.filePath}')"
                          title="${isExpanded ? 'Click to show less' : `Click to see all ${result.suggestions.length} issues`}">
                        ${expandIcon} ${isExpanded ? 'Show less' : `Show ${result.suggestions.length - 3} more issues`}
                    </span>
                </div>` : '';

            return `<li style="margin-bottom: 12px; border: 1px solid var(--vscode-panel-border); border-radius: 6px; padding: 10px; background: var(--vscode-sideBar-background);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <div style="cursor: pointer; color: var(--vscode-textLink-foreground); font-weight: 500;"
                                 onclick="navigateToIssue('${result.filePath}', 1)"
                                 title="Click to open ${result.filePath}">
                                📄 ${fileName}: <span style="color: ${scoreColor};">${result.overallScore}%</span> (${issueCount} issues)
                            </div>
                            <button style="background: var(--vscode-button-background); color: var(--vscode-button-foreground); border: none; border-radius: 4px; padding: 4px 8px; font-size: 11px; cursor: pointer; margin-left: 8px;"
                                    onclick="generatePrompt('${result.filePath}')"
                                    title="Generate AI prompt for fixing issues in this file">
                                📋 Generate AI Prompt
                            </button>
                        </div>
                        ${issuesList}
                        ${expandButton}
                    </li>`;
        }).join('');

        return `
            <div style="margin-bottom: 8px;">
                <b>📊 Review Summary:</b><br>
                Score: <span style="color: ${avgScore >= 80 ? '#4caf50' : avgScore >= 60 ? '#ff9800' : '#f44336'};">${Math.round(avgScore)}%</span><br>
                Issues: ${issuesSummary}
            </div>
            <div style="margin-bottom: 8px;">
                <b>Recent Files:</b>
                <ul style="margin: 4px 0 0 0; padding-left: 18px; font-size: 13px;">
                    ${filesList}
                </ul>
            </div>
        `;
    }

    private getReviewLogHtml(): string {
        // Show loading animation when review is in progress
        if (this._reviewInProgress) {
            const progress = this._reviewProgress.totalFiles > 0
                ? Math.round((this._reviewProgress.reviewedFiles / this._reviewProgress.totalFiles) * 100)
                : 0;

            const currentFileText = this._reviewProgress.currentFile
                ? `Analyzing: ${this._reviewProgress.currentFile}`
                : 'Preparing review...';

            return `
                <div class="review-status">
                    <div class="loading-spinner"></div>
                    <div style="flex: 1;">
                        <div class="pulsing-text" style="font-weight: 500; margin-bottom: 4px;">🤖 AI Review in Progress</div>
                        <div style="font-size: 12px; color: var(--vscode-descriptionForeground); margin-bottom: 8px;">
                            ${currentFileText}
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progress}%;"></div>
                        </div>
                        <div style="font-size: 11px; color: var(--vscode-descriptionForeground); text-align: center; margin-top: 4px;">
                            ${this._reviewProgress.reviewedFiles}/${this._reviewProgress.totalFiles} files • ${progress}% complete
                        </div>
                    </div>
                </div>
                ${this._reviewLog.length > 0 ? `
                    <div style="max-height: 150px; overflow-y: auto; border: 1px solid var(--vscode-input-border); border-radius: 4px; padding: 8px; background: var(--vscode-input-background); margin-top: 8px;">
                        ${this._reviewLog.map(entry =>
                            `<div style="font-family: monospace; font-size: 12px; margin-bottom: 4px; color: var(--vscode-descriptionForeground);">${entry}</div>`
                        ).join('')}
                    </div>
                ` : ''}
            `;
        }

        // Show log entries when review is not in progress
        if (this._reviewLog.length === 0) {
            return '📊 Review activity will be logged here. Start a review to see progress updates.';
        }

        const logEntries = this._reviewLog.map(entry =>
            `<div style="font-family: monospace; font-size: 12px; margin-bottom: 4px; color: var(--vscode-descriptionForeground);">${entry}</div>`
        ).join('');

        return `
            <div style="max-height: 200px; overflow-y: auto; border: 1px solid var(--vscode-input-border); border-radius: 4px; padding: 8px; background: var(--vscode-input-background);">
                ${logEntries}
            </div>
            <div style="margin-top: 8px; font-size: 12px; color: var(--vscode-descriptionForeground);">
                📝 Showing last ${this._reviewLog.length} log entries
            </div>
        `;
    }

    private updateWebview(): void {
        if (this._view) {
            this._view.webview.html = this.getHtmlForWebview(this._view.webview);
        }
    }
}