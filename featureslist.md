### **Git Repository Detection**

- On activation, checks if workspace is a git repo.
- If not, prompts to initialize or open a repo.
- Once a repo is available, proceeds to next steps.

### **Branch and File Change Review**

- Detects active and base branches.
- Shows dropdown to select base branch if needed.
- Lists all files changed in the active branch vs. base.
- User can review all changes or select files.
- AI review process starts, with progress indication.
- Inline suggestions/comments are shown for each file.

### **Inline AI Suggestions & Review Panel**

- Inline feedback appears in the editor (code lens, bubbles, or panel).
- Each suggestion includes:
- Issue type (icon/text)
- Description (what/why)
- Highlighted code
- Suggested fix (with copy/apply)
- User can accept, ignore, or expand for more context.

### **Review Progress & History**

- Status/progress panel shows review state.
- Lists current and recent review sessions/files.
- After completion, user sees a summary of all suggestions.

### **Security & Privacy**

- No code is stored permanently.
- All data is transmitted securely.
- Tokens are stored using VS Code SecretStorage.